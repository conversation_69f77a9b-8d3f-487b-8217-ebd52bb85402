-- 宠物领养系统初始化数据脚本
-- 插入测试数据和默认配置

USE pet_adoption;

-- 插入默认管理员用户（密码：admin123，已加密）
INSERT INTO users (username, password, email, phone, role, real_name, bio) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '<EMAIL>', '13800138000', 'admin', '系统管理员', '宠物领养系统管理员账户'),
('user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '<EMAIL>', '13800138001', 'user', '张三', '热爱动物的普通用户'),
('user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '<EMAIL>', '13800138002', 'user', '李四', '有养宠经验的用户');

-- 插入测试宠物数据
INSERT INTO pets (name, species, breed, age, gender, weight, color, health_status, vaccination_status, is_neutered, description, rescue_location, personality, good_with_kids, good_with_pets, activity_level, training_level) VALUES 
('豆豆', '狗', '金毛寻回犬', 2, '公', 25.50, '金黄色', '健康', '已完成基础疫苗', TRUE, '豆豆是一只非常友善的金毛犬，性格温顺，喜欢和人类互动。它已经接受过基本的训练，会坐下、握手等基本指令。豆豆非常适合有孩子的家庭，它耐心温和，是很好的伴侣犬。', '北京市朝阳区', '温顺友善，活泼好动', TRUE, TRUE, 4, 3),
('咪咪', '猫', '英国短毛猫', 1, '母', 3.20, '银灰色', '健康', '已完成基础疫苗', TRUE, '咪咪是一只可爱的英短猫，性格独立但也很亲人。它喜欢安静的环境，适合上班族饲养。咪咪已经学会使用猫砂盆，生活习惯良好。', '北京市海淀区', '独立安静，偶尔撒娇', TRUE, FALSE, 2, 4),
('小白', '兔', '荷兰兔', 1, '母', 1.80, '白色', '健康', '无需疫苗', FALSE, '小白是一只纯白色的荷兰兔，非常可爱。它性格温顺，适合作为小朋友的宠物。小白需要定期修剪指甲和梳理毛发。', '北京市西城区', '温顺可爱，胆小怕生', TRUE, TRUE, 2, 2),
('小黄', '狗', '中华田园犬', 3, '公', 15.00, '黄色', '健康', '已完成基础疫苗', TRUE, '小黄是一只忠诚的中华田园犬，对主人非常忠诚。它警觉性高，是很好的看家犬。小黄需要足够的运动空间和时间。', '北京市丰台区', '忠诚勇敢，警觉性高', FALSE, FALSE, 5, 2),
('花花', '猫', '中华田园猫', 2, '母', 4.50, '三花色', '健康', '已完成基础疫苗', TRUE, '花花是一只美丽的三花猫，性格活泼好奇。它喜欢探索新环境，但也很亲人。花花适合有经验的养猫家庭。', '北京市东城区', '活泼好奇，聪明机灵', TRUE, TRUE, 3, 3),
('小鸟', '鸟', '虎皮鹦鹉', 1, '未知', 0.05, '绿色', '健康', '无需疫苗', FALSE, '小鸟是一只活泼的虎皮鹦鹉，会说几个简单的词语。它需要宽敞的鸟笼和定期的飞行时间。适合喜欢鸟类的家庭。', '北京市石景山区', '活泼好动，喜欢鸣叫', TRUE, FALSE, 3, 1);

-- 插入测试申请数据
INSERT INTO applications (user_id, pet_id, status, reason, experience, living_situation, family_members, has_other_pets, daily_time, monthly_budget, notes, contact_phone, contact_email) VALUES 
(2, 1, 'pending', 'breed_preference', 'some', 'apartment', 3, FALSE, '3-4', '500-1000', '我们家有三口人，都很喜欢狗狗。我之前养过一只小型犬，有一定的养宠经验。我们住在三居室的公寓里，空间足够大。每天下班后都有时间陪伴宠物，周末还可以带它出去运动。', '13800138001', '<EMAIL>'),
(3, 2, 'approved', 'personal_companion', 'experienced', 'house_no_yard', 2, TRUE, '5-6', '1000-2000', '我是一个资深的猫奴，家里已经有一只猫咪了。我们住在独立的房子里，环境安静适合猫咪生活。我有充足的时间照顾宠物，也有足够的经济能力承担医疗费用。', '13800138002', '<EMAIL>'),
(2, 3, 'rejected', 'companion_for_child', 'none', 'dormitory', 1, FALSE, '1-2', '200-500', '我想为我的孩子找一个小伙伴，但是我住在宿舍里，空间比较小，而且我没有养宠物的经验。', '13800138001', '<EMAIL>');

-- 更新申请的审核信息
UPDATE applications SET reviewer_id = 1, review_time = NOW(), admin_notes = '申请条件符合要求，同意领养。' WHERE id = 2;
UPDATE applications SET reviewer_id = 1, review_time = NOW(), admin_notes = '居住环境不适合养宠物，建议等条件改善后再申请。' WHERE id = 3;

-- 更新已通过申请对应的宠物状态
UPDATE pets SET is_adopted = TRUE, adopter_id = 3, adoption_time = NOW() WHERE id = 2;

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES 
('site_name', '宠物领养中心', '网站名称'),
('site_description', '为流浪宠物寻找温暖的家', '网站描述'),
('contact_phone', '************', '联系电话'),
('contact_email', '<EMAIL>', '联系邮箱'),
('max_applications_per_user', '5', '每个用户最大申请数量'),
('application_review_days', '3', '申请审核天数'),
('upload_max_size', '10485760', '文件上传最大大小（字节）'),
('supported_image_types', 'jpg,jpeg,png,gif', '支持的图片类型');

-- 插入操作日志示例
INSERT INTO operation_logs (user_id, operation, resource_type, resource_id, description, ip_address) VALUES 
(1, 'CREATE_PET', 'pet', 1, '创建宠物：豆豆', '127.0.0.1'),
(1, 'CREATE_PET', 'pet', 2, '创建宠物：咪咪', '127.0.0.1'),
(2, 'CREATE_APPLICATION', 'application', 1, '提交领养申请：申请领养豆豆', '*************'),
(1, 'APPROVE_APPLICATION', 'application', 2, '审核通过申请：用户user2申请领养咪咪', '127.0.0.1'),
(1, 'REJECT_APPLICATION', 'application', 3, '审核拒绝申请：用户user1申请领养小白', '127.0.0.1');
