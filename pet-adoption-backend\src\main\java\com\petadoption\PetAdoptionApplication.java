package com.petadoption;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * 宠物领养系统主应用类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
public class PetAdoptionApplication {

    public static void main(String[] args) {
        SpringApplication.run(PetAdoptionApplication.class, args);
        System.out.println("=================================");
        System.out.println("宠物领养系统后端服务启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("API文档: http://localhost:8080/swagger-ui.html");
        System.out.println("=================================");
    }
}
