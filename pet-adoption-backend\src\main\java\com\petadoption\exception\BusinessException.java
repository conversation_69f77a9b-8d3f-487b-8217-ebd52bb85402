package com.petadoption.exception;

import com.petadoption.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * <AUTHOR> Team
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.INTERNAL_SERVER_ERROR.getCode();
        this.message = message;
    }
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
    
    public BusinessException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
    }
    
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.INTERNAL_SERVER_ERROR.getCode();
        this.message = message;
    }
    
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 创建用户相关异常
     */
    public static BusinessException userNotFound() {
        return new BusinessException(ResultCode.USER_NOT_FOUND);
    }
    
    public static BusinessException userAlreadyExists() {
        return new BusinessException(ResultCode.USER_ALREADY_EXISTS);
    }
    
    public static BusinessException invalidCredentials() {
        return new BusinessException(ResultCode.INVALID_CREDENTIALS);
    }
    
    public static BusinessException tokenInvalid() {
        return new BusinessException(ResultCode.TOKEN_INVALID);
    }
    
    public static BusinessException tokenExpired() {
        return new BusinessException(ResultCode.TOKEN_EXPIRED);
    }
    
    public static BusinessException insufficientPermissions() {
        return new BusinessException(ResultCode.INSUFFICIENT_PERMISSIONS);
    }
    
    /**
     * 创建宠物相关异常
     */
    public static BusinessException petNotFound() {
        return new BusinessException(ResultCode.PET_NOT_FOUND);
    }
    
    public static BusinessException petAlreadyAdopted() {
        return new BusinessException(ResultCode.PET_ALREADY_ADOPTED);
    }
    
    public static BusinessException petNotAvailable() {
        return new BusinessException(ResultCode.PET_NOT_AVAILABLE);
    }
    
    /**
     * 创建申请相关异常
     */
    public static BusinessException applicationNotFound() {
        return new BusinessException(ResultCode.APPLICATION_NOT_FOUND);
    }
    
    public static BusinessException applicationAlreadyExists() {
        return new BusinessException(ResultCode.APPLICATION_ALREADY_EXISTS);
    }
    
    public static BusinessException applicationAlreadyProcessed() {
        return new BusinessException(ResultCode.APPLICATION_ALREADY_PROCESSED);
    }
    
    public static BusinessException applicationPermissionDenied() {
        return new BusinessException(ResultCode.APPLICATION_PERMISSION_DENIED);
    }
    
    /**
     * 创建文件相关异常
     */
    public static BusinessException fileUploadFailed() {
        return new BusinessException(ResultCode.FILE_UPLOAD_FAILED);
    }
    
    public static BusinessException fileNotFound() {
        return new BusinessException(ResultCode.FILE_NOT_FOUND);
    }
    
    public static BusinessException fileTypeNotSupported() {
        return new BusinessException(ResultCode.FILE_TYPE_NOT_SUPPORTED);
    }
    
    public static BusinessException fileSizeExceeded() {
        return new BusinessException(ResultCode.FILE_SIZE_EXCEEDED);
    }
}
