/* 全局样式变量 */
:root {
  --primary: #4e89ae;
  --secondary: #43658b;
  --accent: #ed6663;
  --light: #f7f7f7;
  --dark: #333;
  --success: #4CAF50;
  --warning: #FFC107;
  --danger: #F44336;
  --gray: #e0e0e0;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: var(--dark);
  line-height: 1.6;
}

/* 全局布局 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  max-width: 1200px;
  width: 90%;
  margin: 2rem auto;
  padding: 0 1rem;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: var(--transition);
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: var(--accent);
  color: white;
}

.btn-primary:hover {
  background: #e55a57;
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--primary);
  color: white;
}

.btn-secondary:hover {
  background: var(--secondary);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--accent);
  color: var(--accent);
}

.btn-outline:hover {
  background: var(--accent);
  color: white;
}

.btn-success {
  background: var(--success);
  color: white;
}

.btn-warning {
  background: var(--warning);
  color: white;
}

.btn-danger {
  background: var(--danger);
  color: white;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--secondary);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 137, 174, 0.2);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Flex 工具类 */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.gap-1 {
  gap: 1rem;
}

.gap-2 {
  gap: 2rem;
}

/* 间距工具类 */
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.p-1 { padding: 1rem; }
.p-2 { padding: 2rem; }

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary);
}

.text-secondary {
  color: var(--secondary);
}

.text-accent {
  color: var(--accent);
}

.text-muted {
  color: #666;
}

/* 状态标签 */
.status-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-available {
  background: rgba(76, 175, 80, 0.15);
  color: var(--success);
}

.status-adopted {
  background: rgba(244, 67, 54, 0.15);
  color: var(--danger);
}

.status-pending {
  background: rgba(255, 193, 7, 0.15);
  color: var(--warning);
}

.status-approved {
  background: rgba(76, 175, 80, 0.15);
  color: var(--success);
}

.status-rejected {
  background: rgba(244, 67, 54, 0.15);
  color: var(--danger);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #777;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: var(--gray);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    width: 95%;
    margin: 1rem auto;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
