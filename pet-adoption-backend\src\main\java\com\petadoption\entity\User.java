package com.petadoption.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 用户实体类
 * 对应数据库中的users表
 * 与前端用户数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_username", columnList = "username", unique = true),
    @Index(name = "idx_email", columnList = "email", unique = true),
    @Index(name = "idx_phone", columnList = "phone")
})
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "用户名只能包含字母、数字、下划线和中文")
    @Column(name = "username", nullable = false, unique = true, length = 20)
    private String username;
    
    /**
     * 密码（加密存储）
     */
    @JsonIgnore
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @Column(name = "password", nullable = false, length = 100)
    private String password;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Column(name = "email", nullable = false, unique = true, length = 50)
    private String email;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Column(name = "phone", length = 11)
    private String phone;
    
    /**
     * 用户角色
     * user: 普通用户
     * admin: 管理员
     */
    @NotBlank(message = "用户角色不能为空")
    @Column(name = "role", nullable = false, length = 10)
    private String role = "user";
    
    /**
     * 账户状态
     * true: 启用
     * false: 禁用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;
    
    /**
     * 账户是否锁定
     * true: 锁定
     * false: 未锁定
     */
    @Column(name = "locked", nullable = false)
    private Boolean locked = false;
    
    /**
     * 头像URL
     */
    @Column(name = "avatar_url", length = 255)
    private String avatarUrl;
    
    /**
     * 真实姓名
     */
    @Size(max = 20, message = "真实姓名长度不能超过20个字符")
    @Column(name = "real_name", length = 20)
    private String realName;
    
    /**
     * 身份证号
     */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    @Column(name = "id_card", length = 18)
    private String idCard;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    @Column(name = "address", length = 200)
    private String address;
    
    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    @Column(name = "bio", length = 500)
    private String bio;
    
    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private java.time.LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @Column(name = "last_login_ip", length = 45)
    private String lastLoginIp;
    
    /**
     * 构造函数
     */
    public User() {
        super();
    }
    
    public User(String username, String password, String email) {
        this();
        this.username = username;
        this.password = password;
        this.email = email;
    }
    
    /**
     * 是否为管理员
     */
    public boolean isAdmin() {
        return "admin".equals(this.role);
    }
    
    /**
     * 是否为普通用户
     */
    public boolean isUser() {
        return "user".equals(this.role);
    }
    
    /**
     * 账户是否可用
     */
    public boolean isAccountNonLocked() {
        return !this.locked;
    }
    
    /**
     * 账户是否启用
     */
    public boolean isAccountEnabled() {
        return this.enabled;
    }
}
