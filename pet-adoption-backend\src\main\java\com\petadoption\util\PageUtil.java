package com.petadoption.util;

import lombok.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * 分页工具类
 * 与前端分页组件保持一致的数据格式
 * 
 * <AUTHOR> Team
 */
public class PageUtil {
    
    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE = 1;
    
    /**
     * 默认页面大小
     */
    public static final int DEFAULT_SIZE = 10;
    
    /**
     * 最大页面大小
     */
    public static final int MAX_SIZE = 100;
    
    /**
     * 创建分页对象
     * 
     * @param page 页码（从1开始）
     * @param size 页面大小
     * @return Pageable对象
     */
    public static Pageable createPageable(Integer page, Integer size) {
        return createPageable(page, size, null);
    }
    
    /**
     * 创建分页对象（带排序）
     * 
     * @param page 页码（从1开始）
     * @param size 页面大小
     * @param sort 排序
     * @return Pageable对象
     */
    public static Pageable createPageable(Integer page, Integer size, Sort sort) {
        // 处理页码（前端从1开始，JPA从0开始）
        int pageNumber = (page != null && page > 0) ? page - 1 : 0;
        
        // 处理页面大小
        int pageSize = (size != null && size > 0) ? Math.min(size, MAX_SIZE) : DEFAULT_SIZE;
        
        if (sort != null) {
            return PageRequest.of(pageNumber, pageSize, sort);
        } else {
            return PageRequest.of(pageNumber, pageSize);
        }
    }
    
    /**
     * 创建排序对象
     * 
     * @param direction 排序方向（asc/desc）
     * @param properties 排序字段
     * @return Sort对象
     */
    public static Sort createSort(String direction, String... properties) {
        if (properties == null || properties.length == 0) {
            return Sort.unsorted();
        }
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) 
                ? Sort.Direction.DESC 
                : Sort.Direction.ASC;
        
        return Sort.by(sortDirection, properties);
    }
    
    /**
     * 转换为前端分页响应格式
     * 
     * @param page Spring Data分页对象
     * @return 前端分页响应
     */
    public static <T> PageResponse<T> toPageResponse(Page<T> page) {
        PageResponse<T> response = new PageResponse<>();
        response.setData(page.getContent());
        response.setTotal(page.getTotalElements());
        response.setPage(page.getNumber() + 1); // 转换为前端页码（从1开始）
        response.setSize(page.getSize());
        response.setTotalPages(page.getTotalPages());
        response.setFirst(page.isFirst());
        response.setLast(page.isLast());
        response.setHasNext(page.hasNext());
        response.setHasPrevious(page.hasPrevious());
        return response;
    }
    
    /**
     * 分页响应类
     * 与前端分页组件的数据格式保持一致
     */
    @Data
    public static class PageResponse<T> {
        /**
         * 数据列表
         */
        private List<T> data;
        
        /**
         * 总记录数
         */
        private Long total;
        
        /**
         * 当前页码（从1开始）
         */
        private Integer page;
        
        /**
         * 页面大小
         */
        private Integer size;
        
        /**
         * 总页数
         */
        private Integer totalPages;
        
        /**
         * 是否为第一页
         */
        private Boolean first;
        
        /**
         * 是否为最后一页
         */
        private Boolean last;
        
        /**
         * 是否有下一页
         */
        private Boolean hasNext;
        
        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;
    }
}
