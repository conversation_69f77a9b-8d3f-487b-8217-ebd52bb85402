package com.petadoption.repository;

import com.petadoption.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 * 提供用户相关的数据库操作方法
 * 
 * <AUTHOR> Team
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据手机号查找用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    Optional<User> findByPhone(String phone);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByUsernameOrEmail(String username, String email);
    
    /**
     * 根据角色查找用户
     * 
     * @param role 角色
     * @param pageable 分页参数
     * @return 用户列表
     */
    Page<User> findByRole(String role, Pageable pageable);
    
    /**
     * 根据启用状态查找用户
     * 
     * @param enabled 启用状态
     * @param pageable 分页参数
     * @return 用户列表
     */
    Page<User> findByEnabled(Boolean enabled, Pageable pageable);
    
    /**
     * 根据锁定状态查找用户
     * 
     * @param locked 锁定状态
     * @param pageable 分页参数
     * @return 用户列表
     */
    Page<User> findByLocked(Boolean locked, Pageable pageable);
    
    /**
     * 模糊查询用户（用户名、邮箱、真实姓名）
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE " +
           "u.username LIKE %:keyword% OR " +
           "u.email LIKE %:keyword% OR " +
           "u.realName LIKE %:keyword%")
    Page<User> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 查找指定时间之后注册的用户
     * 
     * @param createTime 注册时间
     * @return 用户列表
     */
    List<User> findByCreateTimeAfter(LocalDateTime createTime);
    
    /**
     * 查找指定时间之后登录的用户
     * 
     * @param lastLoginTime 最后登录时间
     * @return 用户列表
     */
    List<User> findByLastLoginTimeAfter(LocalDateTime lastLoginTime);
    
    /**
     * 统计用户总数
     * 
     * @return 用户总数
     */
    @Query("SELECT COUNT(u) FROM User u")
    long countAllUsers();
    
    /**
     * 统计指定角色的用户数量
     * 
     * @param role 角色
     * @return 用户数量
     */
    long countByRole(String role);
    
    /**
     * 统计启用的用户数量
     * 
     * @return 启用用户数量
     */
    long countByEnabledTrue();
    
    /**
     * 统计今日注册用户数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 注册用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.createTime BETWEEN :startTime AND :endTime")
    long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 更新用户最后登录信息
     * 
     * @param userId 用户ID
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 最后登录IP
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginTime = :lastLoginTime, u.lastLoginIp = :lastLoginIp WHERE u.id = :userId")
    void updateLastLoginInfo(@Param("userId") Long userId, 
                            @Param("lastLoginTime") LocalDateTime lastLoginTime, 
                            @Param("lastLoginIp") String lastLoginIp);
    
    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param password 新密码
     */
    @Modifying
    @Query("UPDATE User u SET u.password = :password WHERE u.id = :userId")
    void updatePassword(@Param("userId") Long userId, @Param("password") String password);
    
    /**
     * 更新用户启用状态
     * 
     * @param userId 用户ID
     * @param enabled 启用状态
     */
    @Modifying
    @Query("UPDATE User u SET u.enabled = :enabled WHERE u.id = :userId")
    void updateEnabled(@Param("userId") Long userId, @Param("enabled") Boolean enabled);
    
    /**
     * 更新用户锁定状态
     * 
     * @param userId 用户ID
     * @param locked 锁定状态
     */
    @Modifying
    @Query("UPDATE User u SET u.locked = :locked WHERE u.id = :userId")
    void updateLocked(@Param("userId") Long userId, @Param("locked") Boolean locked);
    
    /**
     * 批量更新用户状态
     * 
     * @param userIds 用户ID列表
     * @param enabled 启用状态
     */
    @Modifying
    @Query("UPDATE User u SET u.enabled = :enabled WHERE u.id IN :userIds")
    void batchUpdateEnabled(@Param("userIds") List<Long> userIds, @Param("enabled") Boolean enabled);
}
