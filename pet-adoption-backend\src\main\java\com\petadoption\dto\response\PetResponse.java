package com.petadoption.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 宠物响应DTO
 * 与前端宠物数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PetResponse {
    
    /**
     * 宠物ID
     */
    private Long id;
    
    /**
     * 宠物名称
     */
    private String name;
    
    /**
     * 宠物种类
     */
    private String species;
    
    /**
     * 宠物品种
     */
    private String breed;
    
    /**
     * 年龄（岁）
     */
    private Integer age;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 体重（公斤）
     */
    private BigDecimal weight;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 健康状况
     */
    private String healthStatus;
    
    /**
     * 疫苗接种情况
     */
    private String vaccinationStatus;
    
    /**
     * 是否绝育
     */
    private Boolean isNeutered;
    
    /**
     * 详细描述
     */
    private String description;
    
    /**
     * 宠物图片URL
     */
    private String imageUrl;
    
    /**
     * 是否已被领养
     */
    private Boolean isAdopted;
    
    /**
     * 领养者ID
     */
    private Long adopterId;
    
    /**
     * 领养时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime adoptionTime;
    
    /**
     * 救助地点
     */
    private String rescueLocation;
    
    /**
     * 救助时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rescueTime;
    
    /**
     * 特殊需求
     */
    private String specialNeeds;
    
    /**
     * 性格特点
     */
    private String personality;
    
    /**
     * 是否适合有孩子的家庭
     */
    private Boolean goodWithKids;
    
    /**
     * 是否适合与其他宠物相处
     */
    private Boolean goodWithPets;
    
    /**
     * 活动需求等级（1-5）
     */
    private Integer activityLevel;
    
    /**
     * 训练程度（1-5）
     */
    private Integer trainingLevel;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
