-- 宠物领养系统数据库初始化脚本
-- 创建数据库和表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS pet_adoption 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE pet_adoption;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(20) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密）',
    email VARCHAR(50) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(11) COMMENT '手机号',
    role VARCHAR(10) NOT NULL DEFAULT 'user' COMMENT '角色：user-普通用户，admin-管理员',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    locked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否锁定',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    real_name VARCHAR(20) COMMENT '真实姓名',
    id_card VARCHAR(18) COMMENT '身份证号',
    address VARCHAR(200) COMMENT '地址',
    bio VARCHAR(500) COMMENT '个人简介',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 宠物表
CREATE TABLE IF NOT EXISTS pets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '宠物ID',
    name VARCHAR(20) NOT NULL COMMENT '宠物名称',
    species VARCHAR(10) NOT NULL COMMENT '种类：狗、猫、兔、鸟、其他',
    breed VARCHAR(30) NOT NULL COMMENT '品种',
    age INT NOT NULL COMMENT '年龄（岁）',
    gender VARCHAR(5) NOT NULL COMMENT '性别：公、母、未知',
    weight DECIMAL(5,2) COMMENT '体重（公斤）',
    color VARCHAR(20) COMMENT '颜色',
    health_status VARCHAR(50) NOT NULL COMMENT '健康状况',
    vaccination_status VARCHAR(100) COMMENT '疫苗接种情况',
    is_neutered BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否绝育',
    description TEXT NOT NULL COMMENT '详细描述',
    image_url VARCHAR(255) COMMENT '宠物图片URL',
    is_adopted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已被领养',
    adopter_id BIGINT COMMENT '领养者ID',
    adoption_time DATETIME COMMENT '领养时间',
    rescue_location VARCHAR(100) COMMENT '救助地点',
    rescue_time DATETIME COMMENT '救助时间',
    special_needs VARCHAR(200) COMMENT '特殊需求',
    personality VARCHAR(200) COMMENT '性格特点',
    good_with_kids BOOLEAN COMMENT '是否适合有孩子的家庭',
    good_with_pets BOOLEAN COMMENT '是否适合与其他宠物相处',
    activity_level INT COMMENT '活动需求等级（1-5）',
    training_level INT COMMENT '训练程度（1-5）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_species (species),
    INDEX idx_breed (breed),
    INDEX idx_status (is_adopted),
    INDEX idx_name (name),
    INDEX idx_adopter (adopter_id),
    INDEX idx_create_time (create_time),
    
    FOREIGN KEY (adopter_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='宠物表';

-- 领养申请表
CREATE TABLE IF NOT EXISTS applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '申请ID',
    user_id BIGINT NOT NULL COMMENT '申请用户ID',
    pet_id BIGINT NOT NULL COMMENT '申请宠物ID',
    status VARCHAR(10) NOT NULL DEFAULT 'pending' COMMENT '申请状态：pending-待审核，approved-已通过，rejected-已拒绝',
    reason VARCHAR(20) NOT NULL COMMENT '申请理由',
    experience VARCHAR(15) NOT NULL COMMENT '养宠经验：none-没有经验，some-有一些经验，experienced-经验丰富',
    living_situation VARCHAR(20) NOT NULL COMMENT '居住环境',
    family_members INT NOT NULL COMMENT '家庭成员数量',
    has_other_pets BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否有其他宠物',
    other_pets_description VARCHAR(200) COMMENT '其他宠物描述',
    daily_time VARCHAR(10) NOT NULL COMMENT '每日陪伴时间',
    monthly_budget VARCHAR(15) NOT NULL COMMENT '月度预算',
    notes TEXT NOT NULL COMMENT '详细说明',
    admin_notes TEXT COMMENT '管理员备注',
    reviewer_id BIGINT COMMENT '审核人ID',
    review_time DATETIME COMMENT '审核时间',
    contact_phone VARCHAR(11) COMMENT '申请人联系电话',
    contact_email VARCHAR(50) COMMENT '申请人联系邮箱',
    emergency_contact_name VARCHAR(20) COMMENT '紧急联系人姓名',
    emergency_contact_phone VARCHAR(11) COMMENT '紧急联系人电话',
    agree_home_visit BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否同意家访',
    agree_follow_up BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否同意定期回访',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_pet_id (pet_id),
    INDEX idx_status (status),
    INDEX idx_reviewer (reviewer_id),
    INDEX idx_create_time (create_time),
    UNIQUE INDEX idx_user_pet (user_id, pet_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pet_id) REFERENCES pets(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='领养申请表';

-- 系统配置表（可选）
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表（可选）
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    operation VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(20) COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    description VARCHAR(500) COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_create_time (create_time),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
