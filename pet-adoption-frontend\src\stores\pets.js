import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { petApi } from '@/api/pets'

export const usePetsStore = defineStore('pets', () => {
  // 状态
  const pets = ref([])
  const currentPet = ref(null)
  const loading = ref(false)
  const filters = ref({
    species: '',
    status: 'available',
    search: ''
  })
  const pagination = ref({
    current: 1,
    pageSize: 12,
    total: 0
  })

  // 计算属性
  const availablePets = computed(() => 
    pets.value.filter(pet => !pet.is_adopted)
  )

  const adoptedPets = computed(() => 
    pets.value.filter(pet => pet.is_adopted)
  )

  const filteredPets = computed(() => {
    let result = pets.value

    // 按种类筛选
    if (filters.value.species) {
      result = result.filter(pet => pet.species === filters.value.species)
    }

    // 按状态筛选
    if (filters.value.status === 'available') {
      result = result.filter(pet => !pet.is_adopted)
    } else if (filters.value.status === 'adopted') {
      result = result.filter(pet => pet.is_adopted)
    }

    // 按搜索关键词筛选
    if (filters.value.search) {
      const searchLower = filters.value.search.toLowerCase()
      result = result.filter(pet => 
        pet.name.toLowerCase().includes(searchLower) ||
        pet.breed.toLowerCase().includes(searchLower) ||
        pet.description.toLowerCase().includes(searchLower)
      )
    }

    return result
  })

  // 获取宠物列表
  const fetchPets = async (params = {}) => {
    try {
      loading.value = true
      const response = await petApi.getPets({
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...params
      })
      
      pets.value = response.data
      pagination.value.total = response.total
      
      return { success: true }
    } catch (error) {
      console.error('获取宠物列表失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '获取宠物列表失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取宠物详情
  const fetchPetDetail = async (id) => {
    try {
      loading.value = true
      const pet = await petApi.getPetById(id)
      currentPet.value = pet
      
      return { success: true, data: pet }
    } catch (error) {
      console.error('获取宠物详情失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '获取宠物详情失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 添加宠物
  const addPet = async (petData) => {
    try {
      loading.value = true
      const newPet = await petApi.createPet(petData)
      pets.value.unshift(newPet)
      
      return { success: true, data: newPet }
    } catch (error) {
      console.error('添加宠物失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '添加宠物失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 更新宠物
  const updatePet = async (id, petData) => {
    try {
      loading.value = true
      const updatedPet = await petApi.updatePet(id, petData)
      
      const index = pets.value.findIndex(pet => pet.id === id)
      if (index !== -1) {
        pets.value[index] = updatedPet
      }
      
      if (currentPet.value?.id === id) {
        currentPet.value = updatedPet
      }
      
      return { success: true, data: updatedPet }
    } catch (error) {
      console.error('更新宠物失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '更新宠物失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 删除宠物
  const deletePet = async (id) => {
    try {
      loading.value = true
      await petApi.deletePet(id)
      
      pets.value = pets.value.filter(pet => pet.id !== id)
      
      if (currentPet.value?.id === id) {
        currentPet.value = null
      }
      
      return { success: true }
    } catch (error) {
      console.error('删除宠物失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '删除宠物失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 设置筛选条件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      species: '',
      status: 'available',
      search: ''
    }
  }

  // 设置分页
  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  return {
    // 状态
    pets,
    currentPet,
    loading,
    filters,
    pagination,
    
    // 计算属性
    availablePets,
    adoptedPets,
    filteredPets,
    
    // 方法
    fetchPets,
    fetchPetDetail,
    addPet,
    updatePet,
    deletePet,
    setFilters,
    resetFilters,
    setPagination
  }
})
