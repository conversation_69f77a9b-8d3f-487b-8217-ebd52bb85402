package com.petadoption.controller;

import com.petadoption.common.Result;
import com.petadoption.dto.request.LoginRequest;
import com.petadoption.dto.request.RegisterRequest;
import com.petadoption.dto.response.AuthResponse;
import com.petadoption.dto.response.UserResponse;
import com.petadoption.service.AuthService;
import com.petadoption.util.IpUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 处理用户登录、注册、Token验证等认证相关请求
 * 与前端认证API保持一致
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthService authService;
    
    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param httpRequest HTTP请求
     * @return 认证响应
     */
    @PostMapping("/login")
    public Result<AuthResponse> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest
    ) {
        log.info("用户登录请求: {}", request.getUsername());
        
        // 获取客户端IP
        String clientIp = IpUtil.getClientIp(httpRequest);
        
        // 执行登录
        AuthResponse response = authService.login(request, clientIp);
        
        return Result.success("登录成功", response);
    }
    
    /**
     * 用户注册
     * 
     * @param request 注册请求
     * @return 认证响应
     */
    @PostMapping("/register")
    public Result<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
        log.info("用户注册请求: {}", request.getUsername());
        
        // 执行注册
        AuthResponse response = authService.register(request);
        
        return Result.success("注册成功", response);
    }
    
    /**
     * 验证Token
     * 
     * @param authHeader Authorization头
     * @return 用户信息
     */
    @GetMapping("/check")
    public Result<UserResponse> checkToken(@RequestHeader(value = "Authorization", required = false) String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return Result.unauthorized("Token无效");
        }
        
        // 提取Token
        String token = authHeader.substring(7);
        
        // 验证Token
        UserResponse userResponse = authService.validateToken(token);
        
        return Result.success("Token有效", userResponse);
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 当前用户信息
     */
    @GetMapping("/me")
    public Result<UserResponse> getCurrentUser() {
        // 获取当前认证用户
        var currentUser = authService.getCurrentUser();
        
        UserResponse userResponse = UserResponse.builder()
                .id(currentUser.getId())
                .username(currentUser.getUsername())
                .email(currentUser.getEmail())
                .phone(currentUser.getPhone())
                .role(currentUser.getRole())
                .enabled(currentUser.getEnabled())
                .build();
        
        return Result.success("获取用户信息成功", userResponse);
    }
    
    /**
     * 用户登出
     * 
     * @return 响应结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，登出只需要前端删除Token即可
        // 这里可以记录登出日志或执行其他清理操作
        
        try {
            var currentUser = authService.getCurrentUser();
            log.info("用户登出: {} (ID: {})", currentUser.getUsername(), currentUser.getId());
        } catch (Exception e) {
            // 忽略获取用户信息失败的情况
        }
        
        return Result.success("登出成功");
    }
}
