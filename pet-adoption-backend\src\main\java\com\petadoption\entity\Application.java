package com.petadoption.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

/**
 * 领养申请实体类
 * 对应数据库中的applications表
 * 与前端申请数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "applications", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_pet_id", columnList = "pet_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_user_pet", columnList = "user_id, pet_id", unique = true)
})
public class Application extends BaseEntity {
    
    /**
     * 申请用户ID
     */
    @NotNull(message = "申请用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 申请宠物ID
     */
    @NotNull(message = "申请宠物ID不能为空")
    @Column(name = "pet_id", nullable = false)
    private Long petId;
    
    /**
     * 申请状态
     * pending: 待审核
     * approved: 已通过
     * rejected: 已拒绝
     */
    @NotBlank(message = "申请状态不能为空")
    @Column(name = "status", nullable = false, length = 10)
    private String status = "pending";
    
    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    @Column(name = "reason", nullable = false, length = 20)
    private String reason;
    
    /**
     * 养宠经验
     * none: 没有经验
     * some: 有一些经验
     * experienced: 经验丰富
     */
    @NotBlank(message = "养宠经验不能为空")
    @Column(name = "experience", nullable = false, length = 15)
    private String experience;
    
    /**
     * 居住环境
     * house_with_yard: 独立房屋带院子
     * house_no_yard: 独立房屋无院子
     * apartment: 公寓/楼房
     * dormitory: 宿舍
     * other: 其他
     */
    @NotBlank(message = "居住环境不能为空")
    @Column(name = "living_situation", nullable = false, length = 20)
    private String livingSituation;
    
    /**
     * 家庭成员数量
     */
    @NotNull(message = "家庭成员数量不能为空")
    @Min(value = 1, message = "家庭成员数量不能小于1")
    @Max(value = 20, message = "家庭成员数量不能大于20")
    @Column(name = "family_members", nullable = false)
    private Integer familyMembers;
    
    /**
     * 是否有其他宠物
     */
    @NotNull(message = "是否有其他宠物不能为空")
    @Column(name = "has_other_pets", nullable = false)
    private Boolean hasOtherPets = false;
    
    /**
     * 其他宠物描述
     */
    @Column(name = "other_pets_description", length = 200)
    private String otherPetsDescription;
    
    /**
     * 每日陪伴时间
     * 1-2: 1-2小时
     * 3-4: 3-4小时
     * 5-6: 5-6小时
     * full_time: 全天在家
     */
    @NotBlank(message = "每日陪伴时间不能为空")
    @Column(name = "daily_time", nullable = false, length = 10)
    private String dailyTime;
    
    /**
     * 月度预算
     */
    @NotBlank(message = "月度预算不能为空")
    @Column(name = "monthly_budget", nullable = false, length = 15)
    private String monthlyBudget;
    
    /**
     * 详细说明
     */
    @NotBlank(message = "详细说明不能为空")
    @Size(min = 20, max = 500, message = "详细说明长度必须在20-500个字符之间")
    @Column(name = "notes", nullable = false, length = 500)
    private String notes;
    
    /**
     * 管理员备注
     */
    @Column(name = "admin_notes", length = 500)
    private String adminNotes;
    
    /**
     * 审核人ID
     */
    @Column(name = "reviewer_id")
    private Long reviewerId;
    
    /**
     * 审核时间
     */
    @Column(name = "review_time")
    private java.time.LocalDateTime reviewTime;
    
    /**
     * 申请人联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    @Column(name = "contact_phone", length = 11)
    private String contactPhone;
    
    /**
     * 申请人联系邮箱
     */
    @Email(message = "联系邮箱格式不正确")
    @Column(name = "contact_email", length = 50)
    private String contactEmail;
    
    /**
     * 紧急联系人姓名
     */
    @Size(max = 20, message = "紧急联系人姓名长度不能超过20个字符")
    @Column(name = "emergency_contact_name", length = 20)
    private String emergencyContactName;
    
    /**
     * 紧急联系人电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系人电话格式不正确")
    @Column(name = "emergency_contact_phone", length = 11)
    private String emergencyContactPhone;
    
    /**
     * 是否同意家访
     */
    @Column(name = "agree_home_visit", nullable = false)
    private Boolean agreeHomeVisit = true;
    
    /**
     * 是否同意定期回访
     */
    @Column(name = "agree_follow_up", nullable = false)
    private Boolean agreeFollowUp = true;
    
    /**
     * 关联的用户实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 关联的宠物实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pet_id", insertable = false, updatable = false)
    private Pet pet;
    
    /**
     * 关联的审核人实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reviewer_id", insertable = false, updatable = false)
    private User reviewer;
    
    /**
     * 构造函数
     */
    public Application() {
        super();
    }
    
    public Application(Long userId, Long petId) {
        this();
        this.userId = userId;
        this.petId = petId;
    }
    
    /**
     * 是否为待审核状态
     */
    public boolean isPending() {
        return "pending".equals(this.status);
    }
    
    /**
     * 是否已通过审核
     */
    public boolean isApproved() {
        return "approved".equals(this.status);
    }
    
    /**
     * 是否已被拒绝
     */
    public boolean isRejected() {
        return "rejected".equals(this.status);
    }
    
    /**
     * 通过申请
     */
    public void approve(Long reviewerId, String adminNotes) {
        this.status = "approved";
        this.reviewerId = reviewerId;
        this.adminNotes = adminNotes;
        this.reviewTime = java.time.LocalDateTime.now();
    }
    
    /**
     * 拒绝申请
     */
    public void reject(Long reviewerId, String adminNotes) {
        this.status = "rejected";
        this.reviewerId = reviewerId;
        this.adminNotes = adminNotes;
        this.reviewTime = java.time.LocalDateTime.now();
    }
    
    /**
     * 获取申请理由描述
     */
    public String getReasonDescription() {
        switch (reason) {
            case "breed_preference": return "喜欢这个品种";
            case "companion_for_child": return "为孩子寻找伙伴";
            case "personal_companion": return "个人陪伴需求";
            case "experienced_owner": return "有养宠经验";
            case "rescue_animal": return "救助流浪动物";
            case "other": return "其他原因";
            default: return reason;
        }
    }
    
    /**
     * 获取经验描述
     */
    public String getExperienceDescription() {
        switch (experience) {
            case "none": return "没有经验";
            case "some": return "有一些经验";
            case "experienced": return "经验丰富";
            default: return experience;
        }
    }
}
