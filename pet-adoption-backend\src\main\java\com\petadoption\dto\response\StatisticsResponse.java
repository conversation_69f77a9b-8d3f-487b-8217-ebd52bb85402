package com.petadoption.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 统计响应DTO
 * 与前端管理员仪表板数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsResponse {
    
    // ========== 基础统计 ==========
    
    /**
     * 用户总数
     */
    private Long totalUsers;
    
    /**
     * 宠物总数
     */
    private Long totalPets;
    
    /**
     * 可领养宠物数
     */
    private Long availablePets;
    
    /**
     * 已领养宠物数
     */
    private Long adoptedPets;
    
    /**
     * 申请总数
     */
    private Long totalApplications;
    
    /**
     * 待审核申请数
     */
    private Long pendingApplications;
    
    // ========== 今日统计 ==========
    
    /**
     * 今日新增用户
     */
    private Long todayUsers;
    
    /**
     * 今日新增宠物
     */
    private Long todayPets;
    
    /**
     * 今日新增申请
     */
    private Long todayApplications;
    
    /**
     * 今日成功领养
     */
    private Long todayAdoptions;
    
    // ========== 本月统计 ==========
    
    /**
     * 本月新增用户
     */
    private Long monthUsers;
    
    /**
     * 本月新增宠物
     */
    private Long monthPets;
    
    /**
     * 本月新增申请
     */
    private Long monthApplications;
    
    /**
     * 本月成功领养
     */
    private Long monthAdoptions;
    
    // ========== 分类统计 ==========
    
    /**
     * 宠物种类统计
     * key: 种类名称（狗、猫、兔、鸟、其他）
     * value: 数量
     */
    private Map<String, Long> petSpeciesStats;
    
    /**
     * 申请状态统计
     * key: 状态（pending、approved、rejected）
     * value: 数量
     */
    private Map<String, Long> applicationStatusStats;
    
    /**
     * 用户角色统计
     * key: 角色（user、admin）
     * value: 数量
     */
    private Map<String, Long> userRoleStats;
}
