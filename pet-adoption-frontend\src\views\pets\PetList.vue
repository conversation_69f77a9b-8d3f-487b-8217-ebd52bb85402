<template>
  <div class="pet-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-paw"></i>
        宠物列表
      </h1>
      <p class="page-subtitle">找到您心仪的毛孩子，给它们一个温暖的家</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-content">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索宠物名称或品种..."
              size="large"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <i class="fas fa-search"></i>
              </template>
            </el-input>
          </div>

          <!-- 筛选器 -->
          <div class="filters">
            <el-select
              v-model="filters.species"
              placeholder="选择种类"
              size="large"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部种类" value="" />
              <el-option label="狗" value="狗" />
              <el-option label="猫" value="猫" />
              <el-option label="兔" value="兔" />
              <el-option label="鸟" value="鸟" />
              <el-option label="其他" value="其他" />
            </el-select>

            <el-select
              v-model="filters.status"
              placeholder="选择状态"
              size="large"
              @change="handleFilterChange"
            >
              <el-option label="待领养" value="available" />
              <el-option label="已被领养" value="adopted" />
              <el-option label="全部状态" value="all" />
            </el-select>

            <el-select
              v-model="filters.age"
              placeholder="选择年龄"
              size="large"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部年龄" value="" />
              <el-option label="幼年 (0-1岁)" value="young" />
              <el-option label="成年 (1-5岁)" value="adult" />
              <el-option label="老年 (5岁以上)" value="senior" />
            </el-select>

            <el-button
              type="primary"
              size="large"
              @click="resetFilters"
            >
              <i class="fas fa-undo"></i>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 结果统计 -->
    <div class="results-info">
      <div class="results-count">
        <span>找到 <strong>{{ filteredPets.length }}</strong> 只宠物</span>
      </div>
      <div class="view-options">
        <el-button-group>
          <el-button
            :type="viewMode === 'grid' ? 'primary' : 'default'"
            @click="viewMode = 'grid'"
          >
            <i class="fas fa-th"></i>
            网格
          </el-button>
          <el-button
            :type="viewMode === 'list' ? 'primary' : 'default'"
            @click="viewMode = 'list'"
          >
            <i class="fas fa-list"></i>
            列表
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 宠物列表 -->
    <div class="pets-content" v-loading="petsStore.loading">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="pets-grid">
        <div
          v-for="pet in paginatedPets"
          :key="pet.id"
          class="pet-card"
          @click="goToPetDetail(pet.id)"
        >
          <div class="pet-image">
            <i :class="getPetIcon(pet.species)"></i>
            <div class="pet-status-overlay">
              <span :class="getStatusClass(pet.is_adopted, 'pet')">
                {{ getStatusText(pet.is_adopted, 'pet') }}
              </span>
            </div>
          </div>
          <div class="pet-info">
            <h3 class="pet-name">{{ pet.name }}</h3>
            <div class="pet-meta">
              <span class="pet-breed">
                <i class="fas fa-tag"></i>
                {{ pet.breed }}
              </span>
              <span class="pet-age">
                <i class="fas fa-birthday-cake"></i>
                {{ pet.age }}岁
              </span>
              <span class="pet-gender">
                <i class="fas fa-venus-mars"></i>
                {{ pet.gender }}
              </span>
            </div>
            <p class="pet-description">{{ pet.description.substring(0, 60) }}...</p>
            <div class="pet-actions">
              <el-button
                type="primary"
                size="small"
                @click.stop="goToPetDetail(pet.id)"
              >
                <i class="fas fa-info-circle"></i>
                详情
              </el-button>
              <el-button
                v-if="!pet.is_adopted && authStore.isAuthenticated"
                type="success"
                size="small"
                @click.stop="showAdoptionDialog(pet)"
              >
                <i class="fas fa-heart"></i>
                申请领养
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="pets-list">
        <el-card
          v-for="pet in paginatedPets"
          :key="pet.id"
          class="pet-list-item"
          @click="goToPetDetail(pet.id)"
        >
          <div class="list-item-content">
            <div class="pet-image-small">
              <i :class="getPetIcon(pet.species)"></i>
            </div>
            <div class="pet-info-expanded">
              <div class="pet-header">
                <h3 class="pet-name">{{ pet.name }}</h3>
                <span :class="getStatusClass(pet.is_adopted, 'pet')">
                  {{ getStatusText(pet.is_adopted, 'pet') }}
                </span>
              </div>
              <div class="pet-details">
                <span><i class="fas fa-tag"></i> {{ pet.breed }}</span>
                <span><i class="fas fa-birthday-cake"></i> {{ pet.age }}岁</span>
                <span><i class="fas fa-venus-mars"></i> {{ pet.gender }}</span>
                <span><i class="fas fa-heartbeat"></i> {{ pet.health_status }}</span>
              </div>
              <p class="pet-description">{{ pet.description }}</p>
            </div>
            <div class="pet-actions-list">
              <el-button
                type="primary"
                @click.stop="goToPetDetail(pet.id)"
              >
                <i class="fas fa-info-circle"></i>
                详情
              </el-button>
              <el-button
                v-if="!pet.is_adopted && authStore.isAuthenticated"
                type="success"
                @click.stop="showAdoptionDialog(pet)"
              >
                <i class="fas fa-heart"></i>
                申请领养
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredPets.length === 0 && !petsStore.loading" class="empty-state">
        <i class="fas fa-search"></i>
        <h3>没有找到符合条件的宠物</h3>
        <p>请尝试调整筛选条件或搜索关键词</p>
        <el-button type="primary" @click="resetFilters">
          <i class="fas fa-undo"></i>
          重置筛选条件
        </el-button>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredPets.length > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48]"
        :total="filteredPets.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 领养申请对话框 -->
    <AdoptionDialog
      v-model="showDialog"
      :pet="selectedPet"
      @success="handleAdoptionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { usePetsStore } from '@/stores/pets'
import { getPetIcon, getStatusText, getStatusClass, debounce } from '@/utils'
import AdoptionDialog from '@/components/pets/AdoptionDialog.vue'

const router = useRouter()
const authStore = useAuthStore()
const petsStore = usePetsStore()

// 搜索和筛选
const searchQuery = ref('')
const filters = ref({
  species: '',
  status: 'available',
  age: ''
})

// 视图模式
const viewMode = ref('grid')

// 分页
const currentPage = ref(1)
const pageSize = ref(12)

// 领养对话框
const showDialog = ref(false)
const selectedPet = ref(null)

// 计算属性
const filteredPets = computed(() => {
  let result = petsStore.pets

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(pet =>
      pet.name.toLowerCase().includes(query) ||
      pet.breed.toLowerCase().includes(query) ||
      pet.description.toLowerCase().includes(query)
    )
  }

  // 种类筛选
  if (filters.value.species) {
    result = result.filter(pet => pet.species === filters.value.species)
  }

  // 状态筛选
  if (filters.value.status === 'available') {
    result = result.filter(pet => !pet.is_adopted)
  } else if (filters.value.status === 'adopted') {
    result = result.filter(pet => pet.is_adopted)
  }

  // 年龄筛选
  if (filters.value.age) {
    result = result.filter(pet => {
      switch (filters.value.age) {
        case 'young':
          return pet.age <= 1
        case 'adult':
          return pet.age > 1 && pet.age <= 5
        case 'senior':
          return pet.age > 5
        default:
          return true
      }
    })
  }

  return result
})

const paginatedPets = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPets.value.slice(start, end)
})

// 防抖搜索
const handleSearch = debounce(() => {
  currentPage.value = 1
}, 300)

// 筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  filters.value = {
    species: '',
    status: 'available',
    age: ''
  }
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 跳转到宠物详情
const goToPetDetail = (petId) => {
  router.push(`/pets/${petId}`)
}

// 显示领养对话框
const showAdoptionDialog = (pet) => {
  if (!authStore.isAuthenticated) {
    ElMessage.warning('请先登录后再申请领养')
    router.push('/login')
    return
  }
  
  selectedPet.value = pet
  showDialog.value = true
}

// 领养申请成功
const handleAdoptionSuccess = () => {
  ElMessage.success('领养申请提交成功！')
  showDialog.value = false
  selectedPet.value = null
}

// 获取宠物数据
const fetchPets = async () => {
  try {
    await petsStore.fetchPets()
  } catch (error) {
    console.error('获取宠物列表失败:', error)
    ElMessage.error('获取宠物列表失败，请稍后重试')
  }
}

// 监听筛选条件变化
watch(
  () => [searchQuery.value, filters.value],
  () => {
    // 更新store中的筛选条件
    petsStore.setFilters({
      search: searchQuery.value,
      ...filters.value
    })
  },
  { deep: true }
)

onMounted(() => {
  fetchPets()
})
</script>

<style scoped>
.pet-list-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.page-subtitle {
  color: #666;
  font-size: 1.1rem;
}

.filter-section {
  margin-bottom: 2rem;
}

.filter-card {
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-box {
  flex: 1;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filters .el-select {
  min-width: 150px;
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

.results-count {
  color: var(--secondary);
  font-weight: 500;
}

.pets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.pet-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  cursor: pointer;
}

.pet-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.pet-image {
  height: 200px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 4rem;
  position: relative;
}

.pet-status-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
}

.pet-info {
  padding: 1.5rem;
}

.pet-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--secondary);
}

.pet-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  color: #666;
  font-size: 0.9rem;
}

.pet-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pet-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.pet-actions {
  display: flex;
  gap: 0.5rem;
}

.pets-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.pet-list-item {
  cursor: pointer;
  transition: var(--transition);
}

.pet-list-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.list-item-content {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.pet-image-small {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  flex-shrink: 0;
}

.pet-info-expanded {
  flex: 1;
}

.pet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.pet-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.8rem;
  color: #666;
  font-size: 0.9rem;
}

.pet-details span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pet-actions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-shrink: 0;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-content {
    gap: 0.8rem;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .filters .el-select {
    min-width: auto;
  }
  
  .results-info {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .pets-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .list-item-content {
    flex-direction: column;
    text-align: center;
  }
  
  .pet-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .pet-actions-list {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .pets-grid {
    grid-template-columns: 1fr;
  }
  
  .pet-meta,
  .pet-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
