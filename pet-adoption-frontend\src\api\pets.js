import { http } from './request'

export const petApi = {
  /**
   * 获取宠物列表
   * @param {object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页数量
   * @param {string} params.species 种类筛选
   * @param {string} params.status 状态筛选
   * @param {string} params.search 搜索关键词
   * @returns {Promise}
   */
  getPets(params = {}) {
    return http.get('/pets', params)
  },

  /**
   * 根据ID获取宠物详情
   * @param {number} id 宠物ID
   * @returns {Promise}
   */
  getPetById(id) {
    return http.get(`/pets/${id}`)
  },

  /**
   * 创建新宠物
   * @param {object} petData 宠物数据
   * @param {string} petData.name 宠物名称
   * @param {string} petData.species 种类
   * @param {string} petData.breed 品种
   * @param {number} petData.age 年龄
   * @param {string} petData.gender 性别
   * @param {string} petData.health_status 健康状况
   * @param {string} petData.description 描述
   * @returns {Promise}
   */
  createPet(petData) {
    return http.post('/pets', petData)
  },

  /**
   * 更新宠物信息
   * @param {number} id 宠物ID
   * @param {object} petData 宠物数据
   * @returns {Promise}
   */
  updatePet(id, petData) {
    return http.put(`/pets/${id}`, petData)
  },

  /**
   * 删除宠物
   * @param {number} id 宠物ID
   * @returns {Promise}
   */
  deletePet(id) {
    return http.delete(`/pets/${id}`)
  },

  /**
   * 上传宠物图片
   * @param {number} id 宠物ID
   * @param {FormData} formData 图片文件
   * @returns {Promise}
   */
  uploadPetImage(id, formData) {
    return http.upload(`/pets/${id}/image`, formData)
  },

  /**
   * 获取宠物统计数据
   * @returns {Promise}
   */
  getPetStats() {
    return http.get('/pets/stats')
  }
}
