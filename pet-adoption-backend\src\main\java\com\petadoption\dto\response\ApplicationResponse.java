package com.petadoption.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 申请响应DTO
 * 与前端申请数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationResponse {
    
    /**
     * 申请ID
     */
    private Long id;
    
    /**
     * 申请用户ID
     */
    private Long userId;
    
    /**
     * 申请宠物ID
     */
    private Long petId;
    
    /**
     * 申请状态
     */
    private String status;
    
    /**
     * 申请理由
     */
    private String reason;
    
    /**
     * 养宠经验
     */
    private String experience;
    
    /**
     * 居住环境
     */
    private String livingSituation;
    
    /**
     * 家庭成员数量
     */
    private Integer familyMembers;
    
    /**
     * 是否有其他宠物
     */
    private Boolean hasOtherPets;
    
    /**
     * 其他宠物描述
     */
    private String otherPetsDescription;
    
    /**
     * 每日陪伴时间
     */
    private String dailyTime;
    
    /**
     * 月度预算
     */
    private String monthlyBudget;
    
    /**
     * 详细说明
     */
    private String notes;
    
    /**
     * 管理员备注
     */
    private String adminNotes;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;
    
    /**
     * 申请人联系电话
     */
    private String contactPhone;
    
    /**
     * 申请人联系邮箱
     */
    private String contactEmail;
    
    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;
    
    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;
    
    /**
     * 是否同意家访
     */
    private Boolean agreeHomeVisit;
    
    /**
     * 是否同意定期回访
     */
    private Boolean agreeFollowUp;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 关联的用户信息
     */
    private UserInfo user;
    
    /**
     * 关联的宠物信息
     */
    private PetInfo pet;
    
    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        private Long id;
        private String username;
        private String email;
        private String phone;
        private String realName;
    }
    
    /**
     * 宠物信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PetInfo {
        private Long id;
        private String name;
        private String species;
        private String breed;
        private Integer age;
        private String gender;
        private String imageUrl;
        private Boolean isAdopted;
    }
}
