// Token 存储键名
const TOKEN_KEY = 'pet_adoption_token'

/**
 * 获取存储的 token
 * @returns {string|null}
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置 token
 * @param {string} token 
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除 token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 检查 token 是否有效
 * @param {string} token 
 * @returns {boolean}
 */
export function isTokenValid(token) {
  if (!token) return false
  
  try {
    // 解析 JWT token
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    
    // 检查是否过期
    return payload.exp > currentTime
  } catch (error) {
    console.error('Token 解析失败:', error)
    return false
  }
}

/**
 * 从 token 中获取用户信息
 * @param {string} token 
 * @returns {object|null}
 */
export function getUserFromToken(token) {
  if (!token) return null
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return {
      id: payload.userId,
      username: payload.username,
      role: payload.role,
      email: payload.email
    }
  } catch (error) {
    console.error('从 Token 获取用户信息失败:', error)
    return null
  }
}

/**
 * 检查用户是否有指定权限
 * @param {object} user 用户对象
 * @param {string} permission 权限名称
 * @returns {boolean}
 */
export function hasPermission(user, permission) {
  if (!user) return false
  
  // 管理员拥有所有权限
  if (user.role === 'admin') return true
  
  // 根据权限名称检查
  switch (permission) {
    case 'manage_pets':
    case 'manage_applications':
    case 'manage_users':
      return user.role === 'admin'
    case 'apply_adoption':
      return user.role === 'user' || user.role === 'admin'
    default:
      return false
  }
}

/**
 * 格式化用户显示名称
 * @param {object} user 用户对象
 * @returns {string}
 */
export function formatUserDisplayName(user) {
  if (!user) return '未知用户'
  return user.username || user.email || '未知用户'
}

/**
 * 获取用户头像字母
 * @param {object} user 用户对象
 * @returns {string}
 */
export function getUserAvatarLetter(user) {
  if (!user) return '?'
  const name = user.username || user.email || '?'
  return name.charAt(0).toUpperCase()
}
