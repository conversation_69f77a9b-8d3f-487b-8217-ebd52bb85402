package com.petadoption.service;

import com.petadoption.dto.response.StatisticsResponse;
import com.petadoption.repository.ApplicationRepository;
import com.petadoption.repository.PetRepository;
import com.petadoption.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 统计服务
 * 提供系统统计数据，用于管理员仪表板
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {
    
    private final UserRepository userRepository;
    private final PetRepository petRepository;
    private final ApplicationRepository applicationRepository;
    
    /**
     * 获取系统统计数据
     * 
     * @return 统计响应
     */
    @Transactional(readOnly = true)
    public StatisticsResponse getSystemStatistics() {
        log.debug("获取系统统计数据");
        
        // 基础统计
        long totalUsers = userRepository.countAllUsers();
        long totalPets = petRepository.countAllPets();
        long availablePets = petRepository.countByIsAdoptedFalse();
        long adoptedPets = petRepository.countByIsAdoptedTrue();
        long totalApplications = applicationRepository.countAllApplications();
        long pendingApplications = applicationRepository.countByStatus("pending");
        
        // 今日统计
        LocalDateTime todayStart = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime todayEnd = todayStart.plusDays(1);
        
        long todayUsers = userRepository.countByCreateTimeBetween(todayStart, todayEnd);
        long todayPets = petRepository.countByCreateTimeBetween(todayStart, todayEnd);
        long todayApplications = applicationRepository.countByCreateTimeBetween(todayStart, todayEnd);
        long todayAdoptions = applicationRepository.countApprovedByReviewTimeBetween(todayStart, todayEnd);
        
        // 本月统计
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        LocalDateTime monthEnd = monthStart.plusMonths(1);
        
        long monthUsers = userRepository.countByCreateTimeBetween(monthStart, monthEnd);
        long monthPets = petRepository.countByCreateTimeBetween(monthStart, monthEnd);
        long monthApplications = applicationRepository.countByCreateTimeBetween(monthStart, monthEnd);
        long monthAdoptions = applicationRepository.countApprovedByReviewTimeBetween(monthStart, monthEnd);
        
        // 宠物种类统计
        Map<String, Long> petSpeciesStats = new HashMap<>();
        petSpeciesStats.put("狗", petRepository.countBySpecies("狗"));
        petSpeciesStats.put("猫", petRepository.countBySpecies("猫"));
        petSpeciesStats.put("兔", petRepository.countBySpecies("兔"));
        petSpeciesStats.put("鸟", petRepository.countBySpecies("鸟"));
        petSpeciesStats.put("其他", petRepository.countBySpecies("其他"));
        
        // 申请状态统计
        Map<String, Long> applicationStatusStats = new HashMap<>();
        applicationStatusStats.put("pending", applicationRepository.countByStatus("pending"));
        applicationStatusStats.put("approved", applicationRepository.countByStatus("approved"));
        applicationStatusStats.put("rejected", applicationRepository.countByStatus("rejected"));
        
        // 用户角色统计
        Map<String, Long> userRoleStats = new HashMap<>();
        userRoleStats.put("user", userRepository.countByRole("user"));
        userRoleStats.put("admin", userRepository.countByRole("admin"));
        
        return StatisticsResponse.builder()
                // 基础统计
                .totalUsers(totalUsers)
                .totalPets(totalPets)
                .availablePets(availablePets)
                .adoptedPets(adoptedPets)
                .totalApplications(totalApplications)
                .pendingApplications(pendingApplications)
                
                // 今日统计
                .todayUsers(todayUsers)
                .todayPets(todayPets)
                .todayApplications(todayApplications)
                .todayAdoptions(todayAdoptions)
                
                // 本月统计
                .monthUsers(monthUsers)
                .monthPets(monthPets)
                .monthApplications(monthApplications)
                .monthAdoptions(monthAdoptions)
                
                // 分类统计
                .petSpeciesStats(petSpeciesStats)
                .applicationStatusStats(applicationStatusStats)
                .userRoleStats(userRoleStats)
                
                .build();
    }
    
    /**
     * 获取最近7天的趋势数据
     * 
     * @return 趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getWeeklyTrends() {
        log.debug("获取最近7天趋势数据");
        
        Map<String, Object> trends = new HashMap<>();
        
        // 最近7天的数据
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 6; i >= 0; i--) {
            LocalDateTime dayStart = now.minusDays(i).truncatedTo(ChronoUnit.DAYS);
            LocalDateTime dayEnd = dayStart.plusDays(1);
            
            String dateKey = dayStart.toLocalDate().toString();
            
            Map<String, Long> dayData = new HashMap<>();
            dayData.put("users", userRepository.countByCreateTimeBetween(dayStart, dayEnd));
            dayData.put("pets", petRepository.countByCreateTimeBetween(dayStart, dayEnd));
            dayData.put("applications", applicationRepository.countByCreateTimeBetween(dayStart, dayEnd));
            dayData.put("adoptions", applicationRepository.countApprovedByReviewTimeBetween(dayStart, dayEnd));
            
            trends.put(dateKey, dayData);
        }
        
        return trends;
    }
    
    /**
     * 获取最近12个月的趋势数据
     * 
     * @return 月度趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getMonthlyTrends() {
        log.debug("获取最近12个月趋势数据");
        
        Map<String, Object> trends = new HashMap<>();
        
        // 最近12个月的数据
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 11; i >= 0; i--) {
            LocalDateTime monthStart = now.minusMonths(i).withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
            LocalDateTime monthEnd = monthStart.plusMonths(1);
            
            String monthKey = monthStart.getYear() + "-" + String.format("%02d", monthStart.getMonthValue());
            
            Map<String, Long> monthData = new HashMap<>();
            monthData.put("users", userRepository.countByCreateTimeBetween(monthStart, monthEnd));
            monthData.put("pets", petRepository.countByCreateTimeBetween(monthStart, monthEnd));
            monthData.put("applications", applicationRepository.countByCreateTimeBetween(monthStart, monthEnd));
            monthData.put("adoptions", applicationRepository.countApprovedByReviewTimeBetween(monthStart, monthEnd));
            
            trends.put(monthKey, monthData);
        }
        
        return trends;
    }
}
