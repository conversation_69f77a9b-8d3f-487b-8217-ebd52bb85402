import { http } from './request'

export const applicationApi = {
  /**
   * 获取领养申请列表
   * @param {object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页数量
   * @param {string} params.status 状态筛选
   * @param {number} params.userId 用户ID筛选
   * @param {number} params.petId 宠物ID筛选
   * @returns {Promise}
   */
  getApplications(params = {}) {
    return http.get('/applications', params)
  },

  /**
   * 根据ID获取申请详情
   * @param {number} id 申请ID
   * @returns {Promise}
   */
  getApplicationById(id) {
    return http.get(`/applications/${id}`)
  },

  /**
   * 创建领养申请
   * @param {object} applicationData 申请数据
   * @param {number} applicationData.petId 宠物ID
   * @param {string} applicationData.notes 申请说明
   * @returns {Promise}
   */
  createApplication(applicationData) {
    return http.post('/applications', applicationData)
  },

  /**
   * 更新申请状态
   * @param {number} id 申请ID
   * @param {object} statusData 状态数据
   * @param {string} statusData.status 新状态 'pending' | 'approved' | 'rejected'
   * @param {string} statusData.adminNotes 管理员备注
   * @returns {Promise}
   */
  updateApplicationStatus(id, statusData) {
    return http.put(`/applications/${id}/status`, statusData)
  },

  /**
   * 取消申请
   * @param {number} id 申请ID
   * @returns {Promise}
   */
  cancelApplication(id) {
    return http.delete(`/applications/${id}`)
  },

  /**
   * 获取用户的申请列表
   * @param {number} userId 用户ID
   * @param {object} params 查询参数
   * @returns {Promise}
   */
  getUserApplications(userId, params = {}) {
    return http.get(`/users/${userId}/applications`, params)
  },

  /**
   * 获取宠物的申请列表
   * @param {number} petId 宠物ID
   * @param {object} params 查询参数
   * @returns {Promise}
   */
  getPetApplications(petId, params = {}) {
    return http.get(`/pets/${petId}/applications`, params)
  },

  /**
   * 批量处理申请
   * @param {object} batchData 批量操作数据
   * @param {number[]} batchData.applicationIds 申请ID列表
   * @param {string} batchData.action 操作类型 'approve' | 'reject'
   * @param {string} batchData.adminNotes 管理员备注
   * @returns {Promise}
   */
  batchUpdateApplications(batchData) {
    return http.post('/applications/batch', batchData)
  },

  /**
   * 获取申请统计数据
   * @returns {Promise}
   */
  getApplicationStats() {
    return http.get('/applications/stats')
  }
}
