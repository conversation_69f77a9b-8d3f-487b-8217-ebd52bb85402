package com.petadoption.config;

import com.petadoption.security.JwtAuthenticationEntryPoint;
import com.petadoption.security.JwtAuthenticationFilter;
import com.petadoption.service.UserDetailsServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security安全配置
 * 配置JWT认证、权限控制、跨域等安全策略
 * 
 * <AUTHOR> Team
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final UserDetailsServiceImpl userDetailsService;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final CorsConfigurationSource corsConfigurationSource;
    
    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    /**
     * 认证提供者
     */
    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }
    
    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（使用JWT不需要CSRF保护）
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置跨域
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            
            // 配置会话管理（无状态）
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置异常处理
            .exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            
            // 配置请求授权
            .authorizeHttpRequests(auth -> auth
                // 公开接口 - 不需要认证
                .requestMatchers(HttpMethod.POST, "/api/auth/login", "/api/auth/register").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/auth/check").permitAll()
                
                // 宠物相关 - 查看不需要认证，操作需要认证
                .requestMatchers(HttpMethod.GET, "/api/pets/**").permitAll()
                .requestMatchers(HttpMethod.POST, "/api/pets/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PUT, "/api/pets/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/pets/**").hasRole("ADMIN")
                
                // 申请相关 - 需要认证
                .requestMatchers("/api/applications/**").authenticated()
                
                // 用户相关 - 需要认证
                .requestMatchers("/api/users/profile").authenticated()
                .requestMatchers("/api/users/change-password").authenticated()
                .requestMatchers("/api/users/**").hasRole("ADMIN")
                
                // 管理员接口 - 需要管理员权限
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                
                // 文件上传 - 需要认证
                .requestMatchers("/api/upload/**").authenticated()
                
                // 静态资源 - 公开访问
                .requestMatchers("/uploads/**", "/static/**").permitAll()
                
                // 健康检查和监控 - 公开访问
                .requestMatchers("/actuator/health", "/actuator/info").permitAll()
                .requestMatchers("/actuator/**").hasRole("ADMIN")
                
                // API文档 - 开发环境公开，生产环境需要管理员权限
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            )
            
            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            
            // 设置认证提供者
            .authenticationProvider(authenticationProvider());
        
        return http.build();
    }
}
