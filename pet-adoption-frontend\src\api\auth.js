import { http } from './request'

export const authApi = {
  /**
   * 用户登录
   * @param {object} credentials 登录凭据
   * @param {string} credentials.username 用户名
   * @param {string} credentials.password 密码
   * @returns {Promise}
   */
  login(credentials) {
    return http.post('/auth/login', credentials)
  },

  /**
   * 用户注册
   * @param {object} userData 用户数据
   * @param {string} userData.username 用户名
   * @param {string} userData.email 邮箱
   * @param {string} userData.phone 手机号
   * @param {string} userData.password 密码
   * @returns {Promise}
   */
  register(userData) {
    return http.post('/auth/register', userData)
  },

  /**
   * 获取当前用户信息
   * @returns {Promise}
   */
  getCurrentUser() {
    return http.get('/auth/me')
  },

  /**
   * 更新用户信息
   * @param {object} userData 用户数据
   * @returns {Promise}
   */
  updateProfile(userData) {
    return http.put('/auth/profile', userData)
  },

  /**
   * 修改密码
   * @param {object} passwordData 密码数据
   * @param {string} passwordData.oldPassword 旧密码
   * @param {string} passwordData.newPassword 新密码
   * @returns {Promise}
   */
  changePassword(passwordData) {
    return http.put('/auth/password', passwordData)
  },

  /**
   * 刷新token
   * @returns {Promise}
   */
  refreshToken() {
    return http.post('/auth/refresh')
  },

  /**
   * 登出
   * @returns {Promise}
   */
  logout() {
    return http.post('/auth/logout')
  }
}
