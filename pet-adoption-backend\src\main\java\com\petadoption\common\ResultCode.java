package com.petadoption.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 与前端约定的状态码保持一致
 * 
 * <AUTHOR> Team
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_CREDENTIALS(1003, "用户名或密码错误"),
    ACCOUNT_DISABLED(1004, "账户已被禁用"),
    ACCOUNT_LOCKED(1005, "账户已被锁定"),
    PASSWORD_EXPIRED(1006, "密码已过期"),
    TOKEN_INVALID(1007, "Token无效"),
    TOKEN_EXPIRED(1008, "Token已过期"),
    INSUFFICIENT_PERMISSIONS(1009, "权限不足"),
    
    // 宠物相关错误 2xxx
    PET_NOT_FOUND(2001, "宠物不存在"),
    PET_ALREADY_ADOPTED(2002, "宠物已被领养"),
    PET_NOT_AVAILABLE(2003, "宠物不可领养"),
    INVALID_PET_DATA(2004, "宠物数据无效"),
    
    // 申请相关错误 3xxx
    APPLICATION_NOT_FOUND(3001, "申请不存在"),
    APPLICATION_ALREADY_EXISTS(3002, "已存在相同申请"),
    APPLICATION_ALREADY_PROCESSED(3003, "申请已被处理"),
    INVALID_APPLICATION_STATUS(3004, "申请状态无效"),
    APPLICATION_PERMISSION_DENIED(3005, "无权限操作此申请"),
    
    // 文件相关错误 4xxx
    FILE_UPLOAD_FAILED(4001, "文件上传失败"),
    FILE_NOT_FOUND(4002, "文件不存在"),
    FILE_TYPE_NOT_SUPPORTED(4003, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(4004, "文件大小超出限制"),
    
    // 数据库相关错误 5xxx
    DATABASE_ERROR(5001, "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION(5002, "数据完整性约束违反"),
    DUPLICATE_KEY_ERROR(5003, "数据重复"),
    
    // 外部服务错误 6xxx
    EXTERNAL_SERVICE_ERROR(6001, "外部服务调用失败"),
    EMAIL_SEND_FAILED(6002, "邮件发送失败"),
    SMS_SEND_FAILED(6003, "短信发送失败");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态消息
     */
    private final String message;
    
    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
