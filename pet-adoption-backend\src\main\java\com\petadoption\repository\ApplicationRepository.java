package com.petadoption.repository;

import com.petadoption.entity.Application;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 领养申请数据访问层
 * 提供申请相关的数据库操作方法
 * 
 * <AUTHOR> Team
 */
@Repository
public interface ApplicationRepository extends JpaRepository<Application, Long>, JpaSpecificationExecutor<Application> {
    
    /**
     * 根据用户ID查找申请
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据宠物ID查找申请
     * 
     * @param petId 宠物ID
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByPetId(Long petId, Pageable pageable);
    
    /**
     * 根据申请状态查找申请
     * 
     * @param status 申请状态
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据用户ID和宠物ID查找申请
     * 
     * @param userId 用户ID
     * @param petId 宠物ID
     * @return 申请信息
     */
    Optional<Application> findByUserIdAndPetId(Long userId, Long petId);
    
    /**
     * 根据用户ID和状态查找申请
     * 
     * @param userId 用户ID
     * @param status 申请状态
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByUserIdAndStatus(Long userId, String status, Pageable pageable);
    
    /**
     * 根据宠物ID和状态查找申请
     * 
     * @param petId 宠物ID
     * @param status 申请状态
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByPetIdAndStatus(Long petId, String status, Pageable pageable);
    
    /**
     * 根据审核人ID查找申请
     * 
     * @param reviewerId 审核人ID
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByReviewerId(Long reviewerId, Pageable pageable);
    
    /**
     * 查找待审核的申请
     * 
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByStatusOrderByCreateTimeAsc(String status, Pageable pageable);
    
    /**
     * 查找用户的所有申请（包含宠物信息）
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 申请列表
     */
    @Query("SELECT a FROM Application a LEFT JOIN FETCH a.pet WHERE a.userId = :userId ORDER BY a.createTime DESC")
    Page<Application> findByUserIdWithPet(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 查找宠物的所有申请（包含用户信息）
     * 
     * @param petId 宠物ID
     * @param pageable 分页参数
     * @return 申请列表
     */
    @Query("SELECT a FROM Application a LEFT JOIN FETCH a.user WHERE a.petId = :petId ORDER BY a.createTime DESC")
    Page<Application> findByPetIdWithUser(@Param("petId") Long petId, Pageable pageable);
    
    /**
     * 查找所有申请（包含用户和宠物信息）
     * 
     * @param pageable 分页参数
     * @return 申请列表
     */
    @Query("SELECT a FROM Application a LEFT JOIN FETCH a.user LEFT JOIN FETCH a.pet ORDER BY a.createTime DESC")
    Page<Application> findAllWithUserAndPet(Pageable pageable);
    
    /**
     * 根据状态查找申请（包含用户和宠物信息）
     * 
     * @param status 申请状态
     * @param pageable 分页参数
     * @return 申请列表
     */
    @Query("SELECT a FROM Application a LEFT JOIN FETCH a.user LEFT JOIN FETCH a.pet WHERE a.status = :status ORDER BY a.createTime ASC")
    Page<Application> findByStatusWithUserAndPet(@Param("status") String status, Pageable pageable);
    
    /**
     * 查找指定时间段内的申请
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 申请列表
     */
    Page<Application> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 检查用户是否已申请过该宠物
     * 
     * @param userId 用户ID
     * @param petId 宠物ID
     * @return 是否存在申请
     */
    boolean existsByUserIdAndPetId(Long userId, Long petId);
    
    /**
     * 检查用户是否有待审核的申请
     * 
     * @param userId 用户ID
     * @return 是否有待审核申请
     */
    boolean existsByUserIdAndStatus(Long userId, String status);
    
    /**
     * 统计申请总数
     * 
     * @return 申请总数
     */
    @Query("SELECT COUNT(a) FROM Application a")
    long countAllApplications();
    
    /**
     * 统计指定状态的申请数量
     * 
     * @param status 申请状态
     * @return 申请数量
     */
    long countByStatus(String status);
    
    /**
     * 统计用户的申请数量
     * 
     * @param userId 用户ID
     * @return 申请数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计用户指定状态的申请数量
     * 
     * @param userId 用户ID
     * @param status 申请状态
     * @return 申请数量
     */
    long countByUserIdAndStatus(Long userId, String status);
    
    /**
     * 统计宠物的申请数量
     * 
     * @param petId 宠物ID
     * @return 申请数量
     */
    long countByPetId(Long petId);
    
    /**
     * 统计指定时间段内的申请数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 申请数量
     */
    @Query("SELECT COUNT(a) FROM Application a WHERE a.createTime BETWEEN :startTime AND :endTime")
    long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间段内审核通过的申请数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 申请数量
     */
    @Query("SELECT COUNT(a) FROM Application a WHERE a.status = 'approved' AND a.reviewTime BETWEEN :startTime AND :endTime")
    long countApprovedByReviewTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 更新申请状态
     * 
     * @param applicationId 申请ID
     * @param status 新状态
     * @param reviewerId 审核人ID
     * @param adminNotes 管理员备注
     * @param reviewTime 审核时间
     */
    @Modifying
    @Query("UPDATE Application a SET a.status = :status, a.reviewerId = :reviewerId, " +
           "a.adminNotes = :adminNotes, a.reviewTime = :reviewTime WHERE a.id = :applicationId")
    void updateStatus(@Param("applicationId") Long applicationId, 
                     @Param("status") String status, 
                     @Param("reviewerId") Long reviewerId, 
                     @Param("adminNotes") String adminNotes, 
                     @Param("reviewTime") LocalDateTime reviewTime);
    
    /**
     * 批量更新申请状态
     * 
     * @param applicationIds 申请ID列表
     * @param status 新状态
     * @param reviewerId 审核人ID
     * @param reviewTime 审核时间
     */
    @Modifying
    @Query("UPDATE Application a SET a.status = :status, a.reviewerId = :reviewerId, " +
           "a.reviewTime = :reviewTime WHERE a.id IN :applicationIds")
    void batchUpdateStatus(@Param("applicationIds") List<Long> applicationIds, 
                          @Param("status") String status, 
                          @Param("reviewerId") Long reviewerId, 
                          @Param("reviewTime") LocalDateTime reviewTime);
    
    /**
     * 删除用户的所有申请
     * 
     * @param userId 用户ID
     */
    @Modifying
    @Query("DELETE FROM Application a WHERE a.userId = :userId")
    void deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 删除宠物的所有申请
     * 
     * @param petId 宠物ID
     */
    @Modifying
    @Query("DELETE FROM Application a WHERE a.petId = :petId")
    void deleteByPetId(@Param("petId") Long petId);
}
