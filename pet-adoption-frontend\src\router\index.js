import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由组件懒加载
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const PetList = () => import('@/views/pets/PetList.vue')
const PetDetail = () => import('@/views/pets/PetDetail.vue')
const Applications = () => import('@/views/applications/Applications.vue')
const Profile = () => import('@/views/auth/Profile.vue')
const AdminPetManage = () => import('@/views/admin/PetManage.vue')
const AdminPetAdd = () => import('@/views/admin/PetAdd.vue')
const AdminPetEdit = () => import('@/views/admin/PetEdit.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { title: '注册', guest: true }
  },
  {
    path: '/pets',
    name: 'PetList',
    component: PetList,
    meta: { title: '宠物列表' }
  },
  {
    path: '/pets/:id',
    name: 'PetDetail',
    component: PetDetail,
    meta: { title: '宠物详情' }
  },
  {
    path: '/applications',
    name: 'Applications',
    component: Applications,
    meta: { title: '领养申请', requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { title: '个人资料', requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'Admin',
    redirect: '/admin/pets',
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/pets',
    name: 'AdminPetManage',
    component: AdminPetManage,
    meta: { title: '宠物管理', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/pets/add',
    name: 'AdminPetAdd',
    component: AdminPetAdd,
    meta: { title: '添加宠物', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/pets/edit/:id',
    name: 'AdminPetEdit',
    component: AdminPetEdit,
    meta: { title: '编辑宠物', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 宠物领养中心` : '宠物领养中心'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && (!authStore.isAuthenticated || authStore.user?.role !== 'admin')) {
    next('/')
    return
  }
  
  // 已登录用户访问登录/注册页面时重定向到首页
  if (to.meta.guest && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
